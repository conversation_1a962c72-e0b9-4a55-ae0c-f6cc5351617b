import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { Colors } from '@/constants/colors';

export default function AuthCallback() {
  const router = useRouter();
  const params = useLocalSearchParams();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Extract tokens from URL parameters
        const { access_token, refresh_token, error, error_description } = params;

        if (error) {
          console.error('OAuth error:', error, error_description);
          router.replace('/auth/login');
          return;
        }

        if (access_token && refresh_token) {
          // Set the session with the tokens
          const { error: sessionError } = await supabase.auth.setSession({
            access_token: access_token as string,
            refresh_token: refresh_token as string,
          });

          if (sessionError) {
            console.error('Session error:', sessionError);
            router.replace('/auth/login');
          } else {
            // OAuth authentication successful - redirect to main app
            router.replace('/(tabs)');
          }
        } else {
          console.error('Missing tokens in callback');
          router.replace('/auth/login');
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        router.replace('/auth/login');
      }
    };

    handleAuthCallback();
  }, [params, router]);

  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: Colors.background,
    }}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <Text style={{
        marginTop: 16,
        fontSize: 16,
        color: Colors.text,
        textAlign: 'center',
      }}>
        Completing authentication...
      </Text>
    </View>
  );
}
